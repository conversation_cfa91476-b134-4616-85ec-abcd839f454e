# 文章API接口文档

## 概述
本文档描述了文章模块的API接口，包括文章列表和文章详情两个主要功能。

## 接口列表

### 1. 文章列表接口

**接口地址：** `GET /dataapi/article/lists`

**接口描述：** 获取文章列表，支持分页和搜索

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page_no | int | 否 | 页码，默认1 |
| page_size | int | 否 | 每页数量，默认15 |
| title | string | 否 | 文章标题搜索 |
| cid | int | 否 | 文章分类ID |

**响应示例：**
```json
{
    "code": 1,
    "msg": "success",
    "data": {
        "lists": [
            {
                "id": 1,
                "title": "文章标题",
                "desc": "文章简介",
                "image": "https://domain.com/uploads/image.jpg",
                "sort": 100,
                "create_time": 1641024000,
                "create_time_text": "2022-01-01 12:00:00"
            }
        ],
        "count": 10,
        "page_no": 1,
        "page_size": 15,
        "extend": []
    }
}
```

### 2. 文章详情接口

**接口地址：** `GET /dataapi/article/detail`

**接口描述：** 获取文章详情信息

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | int | 是 | 文章ID |

**响应示例：**
```json
{
    "code": 1,
    "msg": "获取成功",
    "data": {
        "id": 1,
        "title": "文章标题",
        "desc": "文章简介",
        "image": "https://domain.com/uploads/image.jpg",
        "content": "<p>文章内容</p>",
        "sort": 100,
        "create_time": 1641024000,
        "create_time_text": "2022-01-01 12:00:00"
    }
}
```

## 数据库表结构

### la_article 文章表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 文章ID |
| cid | int | 文章分类ID |
| title | varchar(255) | 文章标题 |
| desc | varchar(255) | 文章简介 |
| image | varchar(128) | 文章封面图片 |
| content | text | 文章内容 |
| sort | int | 排序值 |
| is_show | tinyint | 是否显示：1-是，0-否 |
| create_time | int | 创建时间 |
| update_time | int | 更新时间 |
| delete_time | int | 删除时间 |

## 文件结构

```
server/app/dataapi/
├── controller/article/
│   └── ArticleController.php          # 文章控制器
├── lists/article/
│   └── ArticleLists.php              # 文章列表逻辑
└── logic/login/
    └── ArticleLogic.php              # 文章详情逻辑
```

## 特性说明

1. **列表功能**：
   - 支持分页查询
   - 支持按标题搜索
   - 支持按分类筛选
   - 只显示已发布的文章（is_show=1）
   - 按排序值和创建时间倒序排列

2. **详情功能**：
   - 验证文章是否存在且已发布
   - 自动处理图片URL域名
   - 格式化时间显示
   - 可扩展浏览量统计功能

3. **安全性**：
   - 接口无需登录即可访问
   - 只返回已发布的文章
   - 参数验证和异常处理

## 使用示例

### 获取文章列表
```bash
curl -X GET "http://your-domain.com/dataapi/article/lists?page_no=1&page_size=10&title=测试"
```

### 获取文章详情
```bash
curl -X GET "http://your-domain.com/dataapi/article/detail?id=1"
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 请求失败 |
| 1 | 请求成功 |

常见错误信息：
- "文章不存在或已下架" - 文章ID无效或文章未发布
- 参数验证错误 - 请求参数格式不正确
