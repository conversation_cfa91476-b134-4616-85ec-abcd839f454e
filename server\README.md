# Data Center Manage - 数据中心管理系统

基于 ThinkPHP 6.0 开发的数据中心管理系统

> 运行环境要求PHP7.1+，兼容PHP8.0。

## 🚀 快速开始

### 环境要求
- PHP 7.1+
- MySQL 5.7+
- Composer

### 安装依赖
```bash
composer install
```

### 环境配置
编辑 `.env` 文件配置数据库等信息

### 启动开发
```bash
php think run
```

## 📖 功能说明

### ✅ 文章管理模块
- **文章列表API**：支持分页、搜索、分类筛选
- **文章详情API**：获取完整文章内容
- **数据表**：la_article（文章表）

### 🚧 其他模块
- 用户管理（开发中）
- 权限管理（开发中）

## 🛠️ 技术栈
- 后端：ThinkPHP 6.0
- 数据库：MySQL
- 部署：支持多种部署方式

## API 文档
详见 [文章API文档](./Docs/Article_API.md)

## 原ThinkPHP信息

[官方应用服务市场](https://market.topthink.com) | [`ThinkAPI`——官方统一API服务](https://docs.topthink.com/think-api)

ThinkPHPV6.0版本由[亿速云](https://www.yisu.com/)独家赞助发布。

## 主要新特性

* 采用`PHP7`强类型（严格模式）
* 支持更多的`PSR`规范
* 原生多应用支持
* 更强大和易用的查询
* 全新的事件系统
* 模型事件和数据库事件统一纳入事件系统
* 模板引擎分离出核心
* 内部功能中间件化
* SESSION/Cookie机制改进
* 对Swoole以及协程支持改进
* 对IDE更加友好
* 统一和精简大量用法

## 安装

~~~
composer create-project topthink/think tp 6.0.*
~~~

如果需要更新框架使用
~~~
composer update topthink/framework
~~~

## 文档

[完全开发手册](https://www.kancloud.cn/manual/thinkphp6_0/content)

## 参与开发

请参阅 [ThinkPHP 核心框架包](https://github.com/top-think/framework)。

## 版权信息

ThinkPHP遵循Apache2开源协议发布，并提供免费使用。

本项目包含的第三方源码和二进制文件之版权信息另行标注。

版权所有Copyright © 2006-2020 by ThinkPHP (http://thinkphp.cn)

All rights reserved。

ThinkPHP® 商标和著作权所有者为上海顶想信息科技有限公司。

更多细节参阅 [LICENSE.txt](LICENSE.txt)
