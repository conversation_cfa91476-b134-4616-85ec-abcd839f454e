<?php

namespace app\dataapi\lists\article;

use app\dataapi\lists\BaseApiDataLists;
use app\common\lists\ListsSearchInterface;
use app\common\model\article\Article;

/**
 * 文章列表
 * Class ArticleLists
 * @package app\dataapi\lists\article
 */
class ArticleLists extends BaseApiDataLists implements ListsSearchInterface
{
    /**
     * @notes 搜索条件
     * @return array
     */
    public function setSearch(): array
    {
       $searchFields = ['keyword'];
        //获取两个数组交集
        return array_intersect(array_keys($this->params),$searchFields);
    }


    /**
     * @notes 获取文章列表
     * @return array
     */
    public function lists(): array
    {
        $lists = Article::field('id,title,desc,image,sort,create_time')
            ->where(['is_show' => 1])
            ->withSearch($this->setSearch(), $this->params)
            ->order('sort desc, create_time desc')
            ->page($this->pageNo, $this->pageSize)
            ->select()
            ->toArray();

        return $lists;
    }

    /**
     * @notes 获取文章总数
     * @return int
     */
    public function count(): int
    {
        return Article::where(['is_show' => 1])->count();
    }
}
