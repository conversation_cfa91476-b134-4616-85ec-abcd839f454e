<?php
/**
 * 文章API测试脚本
 * 用于测试文章列表和详情API是否正常工作
 */

// 引入ThinkPHP框架
require_once __DIR__ . '/vendor/autoload.php';

use think\App;
use think\facade\Db;

// 初始化应用
$app = new App();
$app->initialize();

echo "=== 文章API测试脚本 ===\n\n";

try {
    // 测试数据库连接
    echo "1. 测试数据库连接...\n";
    $dbTest = Db::query('SELECT 1 as test');
    if ($dbTest) {
        echo "✓ 数据库连接正常\n\n";
    }

    // 检查文章表是否存在
    echo "2. 检查文章表结构...\n";
    $tables = Db::query("SHOW TABLES LIKE 'la_article'");
    if (empty($tables)) {
        echo "✗ 文章表 la_article 不存在，请先创建表结构\n";
        echo "可以执行以下SQL创建表：\n";
        echo "CREATE TABLE `la_article` (\n";
        echo "  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '文章id',\n";
        echo "  `cid` int(11) NOT NULL COMMENT '文章分类',\n";
        echo "  `title` varchar(255) NOT NULL COMMENT '文章标题',\n";
        echo "  `desc` varchar(255) NULL DEFAULT '' COMMENT '简介',\n";
        echo "  `image` varchar(128) NULL DEFAULT NULL COMMENT '文章图片',\n";
        echo "  `content` text NULL COMMENT '文章内容',\n";
        echo "  `sort` int(5) NULL DEFAULT 0 COMMENT '排序',\n";
        echo "  `is_show` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否显示:1-是.0-否',\n";
        echo "  `create_time` int(11) NULL DEFAULT NULL COMMENT '创建时间',\n";
        echo "  `update_time` int(11) NULL DEFAULT NULL COMMENT '更新时间',\n";
        echo "  `delete_time` int(11) NULL DEFAULT NULL COMMENT '删除时间',\n";
        echo "  PRIMARY KEY (`id`) USING BTREE\n";
        echo ") ENGINE = InnoDB COMMENT = '文章表' ROW_FORMAT = Dynamic;\n\n";
        exit;
    } else {
        echo "✓ 文章表存在\n\n";
    }

    // 检查是否有测试数据
    echo "3. 检查测试数据...\n";
    $articleCount = Db::name('article')->count();
    echo "当前文章数量: {$articleCount}\n";

    if ($articleCount == 0) {
        echo "正在插入测试数据...\n";
        $testData = [
            [
                'cid' => 1,
                'title' => '测试文章标题1',
                'desc' => '这是一篇测试文章的简介',
                'image' => '/uploads/test1.jpg',
                'content' => '<p>这是测试文章的内容，包含HTML标签。</p><p>第二段内容。</p>',
                'sort' => 100,
                'is_show' => 1,
                'create_time' => time(),
                'update_time' => time()
            ],
            [
                'cid' => 1,
                'title' => '测试文章标题2',
                'desc' => '这是第二篇测试文章的简介',
                'image' => '/uploads/test2.jpg',
                'content' => '<p>第二篇测试文章的内容。</p>',
                'sort' => 90,
                'is_show' => 1,
                'create_time' => time() - 3600,
                'update_time' => time() - 3600
            ]
        ];

        $insertCount = Db::name('article')->insertAll($testData);
        echo "✓ 插入了 {$insertCount} 条测试数据\n\n";
    } else {
        echo "✓ 已有测试数据\n\n";
    }

    // 测试文章列表逻辑
    echo "4. 测试文章列表逻辑...\n";
    $articleLists = new \app\dataapi\lists\article\ArticleLists();
    $lists = $articleLists->lists();
    $count = $articleLists->count();
    
    echo "列表数据条数: " . count($lists) . "\n";
    echo "总记录数: {$count}\n";
    if (!empty($lists)) {
        echo "第一条数据: " . json_encode($lists[0], JSON_UNESCAPED_UNICODE) . "\n";
    }
    echo "✓ 文章列表逻辑测试通过\n\n";

    // 测试文章详情逻辑
    echo "5. 测试文章详情逻辑...\n";
    if (!empty($lists)) {
        $firstArticleId = $lists[0]['id'];
        $detail = \app\dataapi\logic\login\ArticleLogic::detail($firstArticleId);
        
        if ($detail) {
            echo "详情数据: " . json_encode($detail, JSON_UNESCAPED_UNICODE) . "\n";
            echo "✓ 文章详情逻辑测试通过\n\n";
        } else {
            echo "✗ 文章详情逻辑测试失败: " . \app\dataapi\logic\login\ArticleLogic::getError() . "\n\n";
        }
    }

    echo "=== 测试完成 ===\n";
    echo "API访问地址:\n";
    echo "文章列表: GET /dataapi/article/lists\n";
    echo "文章详情: GET /dataapi/article/detail?id=1\n";

} catch (Exception $e) {
    echo "✗ 测试过程中出现错误: " . $e->getMessage() . "\n";
    echo "错误文件: " . $e->getFile() . ":" . $e->getLine() . "\n";
}
