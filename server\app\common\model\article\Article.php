<?php

namespace app\common\model\article;

use app\common\enum\YesNoEnum;
use app\common\model\BaseModel;
use think\model\concern\SoftDelete;
use app\common\service\FileService;
/**
 * 资讯管理模型
 * Class Article
 * @package app\common\model\article;
 */
class Article extends BaseModel
{
    use SoftDelete;

    protected $deleteTime = 'delete_time';

    /**
     * @notes  获取分类名称
     * @param $value
     * @param $data
     * @return string
     */
    public function getCateNameAttr($value, $data)
    {
        return ArticleCate::where('id', $data['cid'])->value('name');
    }

    /**
     * @notes 浏览量
     * @param $value
     * @param $data
     * @return mixed
     */
    public function getClickAttr($value, $data)
    {
        return $data['click_actual'] + $data['click_virtual'];
    }

    /**
     * @notes 搜索器-文章标题
     * @param $query
     * @param $value
     * @param $data
     */
    public function searchKeywordAttr($query, $value, $data)
    {
        if (!empty($value)) {
            $query->where('title|desc', 'like', '%' . $value . '%');
        }
    }

    /**
     * @notes 设置图片域名
     * @param $value
     * @param $data
     * @return array|string|string[]|null
     */
    public function getContentAttr($value, $data)
    {
        return (!empty($value) && is_string($value)) ? FileService::getFileUrl(trim($value)) : '';
    }


    /**
     * @notes 获取文章详情
     * @param $id
     * @return array
     */
    public static function getArticleDetailArr(int $id)
    {
        $article = Article::where(['id' => $id, 'is_show' => YesNoEnum::YES])
            ->findOrEmpty();

        if ($article->isEmpty()) {
            return [];
        }

        // 增加点击量
        $article->click_actual += 1;
        $article->save();

        return $article->append(['click'])
            ->hidden(['click_virtual', 'click_actual'])
            ->toArray();
    }

}