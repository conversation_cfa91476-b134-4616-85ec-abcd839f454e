<?php

namespace app\dataapi\validate\article;


use app\common\validate\BaseValidate;


/**
 * 文章验证
 * Class ArticleValidate
 * @package app\dataapi\validate\article
 */
class ArticleValidate extends BaseValidate
{
    protected $rule = [
        'id'        => 'require',
    ];

    protected $message = [
        'id.require'    => '缺少参数',
    ];

    /**
     * @notes 文章详情场景
     * @return LoginValidate
     */
    public function sceneDetail()
    {
        return $this->only(['id']);
    }
}
