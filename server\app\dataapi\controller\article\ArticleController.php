<?php

namespace app\dataapi\controller\article;

use app\dataapi\controller\BaseApiController;
use app\dataapi\lists\article\ArticleLists;
use app\dataapi\validate\article\ArticleValidate;
use app\dataapi\logic\article\ArticleLogic;

/**
 * 文章控制器
 * Class ArticleController
 * @package app\dataapi\controller\article
 */
class ArticleController extends BaseApiController
{
    /**
     * @notes 文章列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025-01-08
     */
    public function lists()
    {
        return $this->dataLists(new ArticleLists());
    }

    /**
     * @notes 文章详情
     * @return \think\response\Json
     * @date 2025-01-08
     */
    public function detail()
    {
        $params = (new ArticleValidate())->goCheck('detail');

        $result = ArticleLogic::detail($params);
        if (false === $result) {
            return $this->fail(ArticleLogic::getError());
        }
        return $this->success('获取成功', $result, 1, 0);
    }
}
